<template>
  <div class="snow-page">
    <div class="snow-inner">
      <a-form ref="formRef" auto-label-width :model="formData.form">
        <a-row :gutter="16">
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" :xxl="6">
            <a-form-item field="examName" label="考试名称">
              <a-input v-model="formData.form.examName" placeholder="请输入名称" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" :xxl="6">
            <a-form-item field="school" label="学校">
              <a-select v-model="formData.form.school" placeholder="请选择学校" allow-clear>
                <a-option value="厦门大学">厦门大学</a-option>
                <a-option value="清华大学">清华大学</a-option>
                <a-option value="北京大学">北京大学</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" :xxl="6">
            <a-form-item field="examType" label="考试类型">
              <a-select v-model="formData.form.examType" placeholder="请选择类型" allow-clear>
                <a-option value="期中考试">期中考试</a-option>
                <a-option value="期末考试">期末考试</a-option>
                <a-option value="模拟考试">模拟考试</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="6" :xxl="6">
            <a-space class="search-btn">
              <a-button type="primary" @click="getCommonTableList">
                <template #icon>
                  <icon-search />
                </template>
                <template #default>查询</template>
              </a-button>
              <a-button @click="onReset">
                <template #icon>
                  <icon-refresh />
                </template>
                <template #default>重置</template>
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>

      <!-- 操作按钮区域 -->
      <div class="operation-buttons">
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <icon-plus />
            </template>
            新增考试
          </a-button>
          <a-button type="primary" status="danger" @click="handleBatchDelete" :disabled="selectedKeys.length === 0">
            <template #icon>
              <icon-delete />
            </template>
            批量删除
          </a-button>
        </a-space>
      </div>
      <a-table
        row-key="key"
        size="small"
        :bordered="{
          cell: true
        }"
        :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
        :loading="loading"
        :columns="columns"
        :data="data"
        :row-selection="rowSelection"
        v-model:selectedKeys="selectedKeys"
        :pagination="pagination"
        @page-change="pageChange"
        @page-size-change="pageSizeChange"
      >
        <template #examStatus="{ record }">
          <a-space>
            <a-tag size="small" color="green" v-if="record.examStatus === '上架'">上架</a-tag>
            <a-tag size="small" color="orange" v-else-if="record.examStatus === '草稿'">草稿</a-tag>
            <a-tag size="small" color="red" v-else-if="record.examStatus === '下架'">下架</a-tag>
            <a-tag size="small" color="blue" v-else>{{ record.examStatus }}</a-tag>
          </a-space>
        </template>
        <template #optional="{ record }">
          <a-space>
            <a-button size="mini" type="text" @click="handleView(record)">
              <icon-eye />
            </a-button>
            <a-button size="mini" type="text" @click="handleEdit(record)">
              <icon-edit />
            </a-button>
            <a-popconfirm content="确定删除这条数据吗?" type="warning" @ok="handleDelete(record)">
              <a-button size="mini" type="text" status="danger">
                <icon-delete />
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { getCommonTableListAPI } from "@/api/modules/table/index";
import { List, FormData, RowSelection, Pagination } from "./config";
const formData = reactive<FormData>({
  form: {
    examName: "",
    school: "",
    examType: ""
  },
  search: false
});
const selectedKeys = ref<string[]>([]);
const rowSelection = reactive<RowSelection>({
  type: "checkbox",
  showCheckedAll: true,
  onlyCurrent: false
});
const pagination = ref<Pagination>({ showPageSize: true, showTotal: true, current: 1, pageSize: 10, total: 100 });
const pageChange = (page: number) => {
  pagination.value.current = page;
};
const pageSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize;
};
const columns = [
  {
    title: "考试编号",
    dataIndex: "examId",
    width: 120
  },
  {
    title: "考试名称",
    dataIndex: "examName",
    width: 200
  },
  {
    title: "学校编号",
    dataIndex: "schoolId",
    width: 120
  },
  {
    title: "学校名称",
    dataIndex: "schoolName",
    width: 150
  },
  {
    title: "考试时间",
    dataIndex: "examTime",
    width: 120
  },
  {
    title: "考试类型",
    dataIndex: "examType",
    width: 120
  },
  {
    title: "保存路径",
    dataIndex: "savePath",
    width: 200,
    ellipsis: true,
    tooltip: true
  },
  {
    title: "创建日期",
    dataIndex: "createDate",
    width: 120
  },
  {
    title: "创建者",
    dataIndex: "creator",
    width: 100
  },
  {
    title: "考试状态",
    dataIndex: "examStatus",
    align: "center",
    slotName: "examStatus",
    width: 100
  },
  {
    title: "操作",
    slotName: "optional",
    align: "center",
    fixed: "right",
    width: 120
  }
];
const formRef = ref();
const onReset = () => {
  formRef.value.resetFields();
  getCommonTableList();
};
const loading = ref<boolean>(false);
const data = reactive<List[]>([]);
const getCommonTableList = async () => {
  try {
    loading.value = true;
    // let res = await getCommonTableListAPI();
    // 模拟考试数据
    const mockData = Array.from({ length: 10 }, (_, index) => ({
      key: String(index + 1),
      examId: "567890234",
      examName: "大学中期期末考试",
      schoolId: "35780",
      schoolName: "厦门大学",
      examTime: "2025-06-19",
      examType: "七年级",
      savePath: "D:\\厦门大学语文",
      createDate: "已发布",
      creator: "admin",
      examStatus: "上架"
    }));
    Object.assign(data, mockData);
    pagination.value.total = 100;
  } finally {
    loading.value = false;
  }
};

// 新增考试
const handleAdd = () => {
  console.log("新增考试");
};

// 批量删除
const handleBatchDelete = () => {
  console.log("批量删除", selectedKeys.value);
};

// 查看详情
const handleView = (record: any) => {
  console.log("查看详情", record);
};

// 编辑
const handleEdit = (record: any) => {
  console.log("编辑", record);
};

// 删除
const handleDelete = (record: any) => {
  console.log("删除", record);
};

getCommonTableList();
</script>

<style lang="scss" scoped>
.search-btn {
  margin-bottom: 20px;
}
.operation-buttons {
  padding: 16px 0;
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}
</style>
